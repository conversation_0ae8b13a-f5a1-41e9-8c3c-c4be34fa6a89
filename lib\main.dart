import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart'; // Import for localizations
import 'l10n/app_localizations.dart';

import 'providers/ccp_accounts_provider.dart';
import 'providers/expansion_provider.dart';
import 'providers/locale_provider.dart'; // Import LocaleProvider
import 'providers/app_state_provider.dart'; // Import AppStateProvider
import 'screens/main_screen.dart';
import 'services/ad_service.dart'; // Import AdService

void main() async {
  // Assurez-vous que les widgets Flutter sont initialisés
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser le service d'annonces
  await AdService().initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => CCPAccountsProvider()),
        ChangeNotifierProvider(create: (context) => ExpansionProvider()),
        ChangeNotifierProvider(create: (context) => LocaleProvider()), // Add LocaleProvider
        ChangeNotifierProvider(create: (context) => AppStateProvider()), // Add AppStateProvider
      ],
      child: Consumer<LocaleProvider>( // Consume LocaleProvider
        builder: (context, localeProvider, child) {
          return MaterialApp(
            title: 'Calculateur CCP RIP',
            debugShowCheckedModeBanner: false,
            locale: localeProvider.locale, // Set locale from provider
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', ''),
              Locale('fr', ''),
              Locale('ar', ''),
            ],
            theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF0D47A1), // Bleu foncé
            brightness: Brightness.light,
            primary: const Color(0xFF0D47A1),
            secondary: const Color(0xFFFFA000), // Jaune orangé
            tertiary: const Color(0xFF212121), // Noir
            surface: Colors.white,
            background: const Color(0xFF212121),
          ),
          useMaterial3: true,
          scaffoldBackgroundColor: const Color(0xFFF5F5F5), // Gris très clair presque blanc
          textTheme: GoogleFonts.poppinsTextTheme(
            Theme.of(context).textTheme,
          ).copyWith(
            titleLarge: GoogleFonts.poppins(
              fontSize: 22,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.15,
              color: const Color(0xFF263238),
            ),
            titleMedium: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.1,
              color: const Color(0xFF263238),
            ),
            bodyLarge: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.normal,
              letterSpacing: 0.5,
              color: const Color(0xFF37474F),
            ),
            bodyMedium: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              letterSpacing: 0.25,
              color: const Color(0xFF455A64),
            ),
          ),
          appBarTheme: AppBarTheme(
            backgroundColor: const Color(0xFF1976D2), // Bleu principal
            foregroundColor: Colors.white,
            elevation: 0,
            centerTitle: true, // Centre le titre
            titleSpacing: 8, // Espace minimal pour le titre
            iconTheme: const IconThemeData(
              size: 22, // Taille réduite des icônes
              color: Colors.white, // Couleur blanche pour les icônes
            ),
            actionsIconTheme: const IconThemeData(
              size: 22, // Taille réduite des icônes d'action
              color: Colors.white, // Couleur blanche pour les icônes d'action
            ),
            titleTextStyle: GoogleFonts.poppins(
              fontSize: 16, // Taille réduite du titre
              fontWeight: FontWeight.w600,
              letterSpacing: 0.1,
              color: Colors.white,
            ),
            toolbarHeight: 48, // Hauteur réduite de la barre d'outils
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(16),
              ),
            ),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: const Color(0xFF1976D2), // Bleu principal
              elevation: 3,
              shadowColor: const Color(0x801976D2),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              textStyle: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
          ),
          cardTheme: CardThemeData(
            elevation: 4,
            shadowColor: Colors.black.withOpacity(0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            color: Colors.white,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
          ),
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF0D47A1), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFD50000), width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFD50000), width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 18,
            ),
            labelStyle: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF455A64),
            ),
            hintStyle: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.grey[500],
            ),
            prefixIconColor: const Color(0xFF455A64),
            suffixIconColor: const Color(0xFF455A64),
          ),
          bottomNavigationBarTheme: BottomNavigationBarThemeData(
            backgroundColor: Colors.white,
            selectedItemColor: const Color(0xFF0D47A1), // Bleu foncé
            unselectedItemColor: Colors.grey[600],
            type: BottomNavigationBarType.fixed,
            elevation: 8,
            selectedLabelStyle: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            unselectedLabelStyle: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
            selectedIconTheme: const IconThemeData(
              size: 24,
            ),
            unselectedIconTheme: const IconThemeData(
              size: 22,
            ),
          ),
        ),
        // MainScreen sans showcase
        home: const MainScreen(),
        // Update MaterialApp title using AppLocalizations after context is available
        // This can be done by wrapping MaterialApp with a Builder or using onGenerateTitle
        onGenerateTitle: (BuildContext context) {
          // Ensure AppLocalizations is available, otherwise fallback
          return AppLocalizations.of(context).appTitle ?? 'CCP_RIP';
        },
        routes: {
          '/main': (context) => const MainScreen(),
        },
          );
        },
      ),
    );
  }
}
