// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Calculateur CCP RIP';

  @override
  String get bottomNavCalculate => 'Calculer';

  @override
  String get bottomNavAccounts => 'Comptes';

  @override
  String get bottomNavVerify => 'Vérifier';

  @override
  String get homeScreenTitle => 'Calculateur CCP RIP';

  @override
  String get savedAccountsScreenTitle => 'Comptes Enregistrés';

  @override
  String get verifyRipScreenTitle => 'Vérifier Compte RIP';

  @override
  String get menuPrivacyPolicy => 'Politique de confidentialité';

  @override
  String get menuRateApp => 'Noter l\'application';

  @override
  String get menuUpdate => 'Mise à jour';

  @override
  String get menuAbout => 'À propos';

  @override
  String get menuQuit => 'Quitter';

  @override
  String get menuLocalBackup => 'Sauvegarde Locale';

  @override
  String get cancel => 'Annuler';

  @override
  String get confirm => 'Confirmer';

  @override
  String get localBackupTitle => 'Sauvegarde et Restauration';

  @override
  String get localBackupExport => 'Exporter vos comptes';

  @override
  String get localBackupExportDescription =>
      'Créez une sauvegarde sécurisée de tous vos comptes CCP dans un fichier que vous pouvez conserver sur votre appareil ou partager en toute sécurité.';

  @override
  String get localBackupExportButton => 'Exporter mes comptes';

  @override
  String get localBackupExportSuccess =>
      '✅ Vos comptes ont été exportés avec succès';

  @override
  String get localBackupExportError =>
      '❌ Impossible d\'exporter vos comptes. Veuillez réessayer.';

  @override
  String get localBackupImport => 'Importer des comptes';

  @override
  String get localBackupImportDescription =>
      'Restaurez vos comptes à partir d\'un fichier de sauvegarde précédemment créé. Sélectionnez le fichier JSON contenant vos données.';

  @override
  String get localBackupImportHint => 'Collez le contenu JSON ici';

  @override
  String get localBackupImportButton => 'Sélectionner un fichier';

  @override
  String get localBackupImportFilePrompt =>
      'Sélectionnez un fichier JSON à importer :';

  @override
  String get localBackupImportConfirmTitle => 'Confirmer l\'importation';

  @override
  String get localBackupImportConfirmMessage =>
      'Cette action ajoutera les comptes du fichier de sauvegarde à votre liste actuelle. Vos comptes existants seront conservés. Souhaitez-vous continuer ?';

  @override
  String localBackupImportSuccess(Object count) {
    return '✅ $count compte(s) importé(s) avec succès';
  }

  @override
  String get localBackupImportNoAccounts =>
      'ℹ️ Aucun nouveau compte trouvé dans le fichier';

  @override
  String get localBackupImportError =>
      '❌ Impossible d\'importer les comptes. Vérifiez que le fichier est valide.';

  @override
  String get localBackupNote =>
      'Remarque : L\'importation de comptes ne supprimera pas vos comptes existants. Les nouveaux comptes seront ajoutés à vos comptes existants.';

  @override
  String get verifyRipScannerTooltip => 'Scanner Code RIP';

  @override
  String get changeLanguageTooltip => 'Changer de langue';

  @override
  String get verifyRipHeaderTitle => 'Vérification de Compte RIP';

  @override
  String get verifyRipHeaderSubtitle =>
      'Entrez un code RIP pour vérifier sa validité';

  @override
  String get verifyRipInputLabel => 'Compte RIP';

  @override
  String get verifyRipInputHint => 'Format: 00799999xxxxxxxxxxxx';

  @override
  String get verifyRipPasteTooltip => 'Coller depuis le presse-papiers';

  @override
  String get verifyRipButtonText => 'VÉRIFIER';

  @override
  String get verifyRipValidationEmpty => 'Veuillez entrer un compte RIP';

  @override
  String get verifyRipValidationLength =>
      'Le code RIP doit contenir exactement 20 chiffres';

  @override
  String get verifyRipValidationPrefix =>
      'Le code RIP doit commencer par 00799999';

  @override
  String get verifyRipResultValid => 'Compte RIP valide';

  @override
  String get verifyRipResultInvalid => 'Compte RIP invalide';

  @override
  String get verifyRipResultValidSub => 'Le code RIP a été vérifié avec succès';

  @override
  String get verifyRipResultInvalidSub => 'Le code RIP saisi est incorrect';

  @override
  String get verifyRipInfoTitle => 'Informations du compte';

  @override
  String get verifyRipInfoPrefixLabel => 'Préfixe';

  @override
  String get verifyRipInfoBankCodeLabel => 'Code Banque';

  @override
  String get verifyRipInfoCcpNumberLabel => 'Numéro CCP';

  @override
  String get verifyRipInfoCcpKeyLabel => 'Clé CCP';

  @override
  String get verifyRipInfoRipKeyLabel => 'Clé RIP';

  @override
  String get verifyRipFullRipLabel => 'Compte RIP';

  @override
  String get verifyRipCopyButton => 'Copier';

  @override
  String get verifyRipCopiedMessage => 'Compte RIP copié';

  @override
  String get verifyRipGenericInvalidMessage =>
      'Le compte RIP saisi est incorrect. Veuillez vérifier et réessayer.';

  @override
  String get changeLanguageDialogTitle => 'Changer de langue';

  @override
  String get ccpFormInputLabel => 'Numéro CCP';

  @override
  String get ccpFormInputHint => 'Entrez le numéro CCP';

  @override
  String get ccpFormValidationEmpty => 'Veuillez entrer un numéro CCP';

  @override
  String get ccpFormValidationInvalid => 'Veuillez entrer un numéro CCP valide';

  @override
  String get ccpFormResultTitle => 'Calcul effectué avec succès';

  @override
  String get ccpFormResultSubtitle => 'Votre code RIP est prêt à être utilisé';

  @override
  String get ccpFormCcpKeyLabel => 'Clé CCP';

  @override
  String get ccpFormRipKeyLabel => 'Clé RIP';

  @override
  String get ccpFormRipAccountLabel => 'Compte RIP';

  @override
  String get ccpFormSaveButton => 'ENREGISTRER LE COMPTE';

  @override
  String get ccpFormSaveButtonShort => 'ENREGISTRER';

  @override
  String get ccpFormAccountExistsError =>
      'Un compte avec ce code RIP existe déjà';

  @override
  String get ccpFormClearTooltip => 'Effacer';

  @override
  String get savedAccountsImportTooltip =>
      'Importer des comptes depuis un fichier';

  @override
  String get savedAccountsExportTooltip => 'Exporter tous mes comptes';

  @override
  String get savedAccountsSearchLabel => 'Rechercher';

  @override
  String get savedAccountsSearchHint => 'Rechercher par nom ou numéro CCP';

  @override
  String get savedAccountsEmptySearch => 'Aucun compte trouvé';

  @override
  String get savedAccountsEmpty => 'Aucun compte enregistré';

  @override
  String get savedAccountsEditDialogTitle => 'Modifier le Compte';

  @override
  String get savedAccountsEditOwnerNameLabel => 'Nom du propriétaire :';

  @override
  String get savedAccountsEditOwnerNameHint => 'Entrez le nom';

  @override
  String get savedAccountsDialogCancel => 'ANNULER';

  @override
  String get savedAccountsDialogSave => 'ENREGISTRER';

  @override
  String savedAccountsImportSuccess(int count) {
    return '$count comptes importés avec succès';
  }

  @override
  String savedAccountsImportError(String error) {
    return 'Erreur lors de l\'importation des comptes: $error';
  }

  @override
  String savedAccountsExportError(String error) {
    return 'Erreur lors de l\'exportation des comptes: $error';
  }

  @override
  String get savedAccountsManageTitle => 'Gestion des comptes';

  @override
  String get savedAccountsImportTitle => 'Importer des comptes';

  @override
  String get savedAccountsExportTitle => 'Exporter mes comptes';

  @override
  String get saveAccountScreenTitle => 'Enregistrer le Compte';

  @override
  String get saveAccountOwnerNameHint => 'Entrez le nom du propriétaire';

  @override
  String get saveAccountOwnerNameValidation =>
      'Veuillez entrer le nom du propriétaire';

  @override
  String get saveAccountSuccessMessage => 'Compte enregistré avec succès';

  @override
  String get saveAccountGenericErrorPrefix => 'Erreur: ';

  @override
  String get savedAccountLastModified => 'Modifié le:';

  @override
  String get savedAccountCcpLabel => 'CCP:';

  @override
  String get savedAccountRipLabel => 'Compte RIP:';

  @override
  String get savedAccountCopyRip => 'Copier RIP';

  @override
  String get savedAccountShare => 'Partager';

  @override
  String get savedAccountEdit => 'Modifier';

  @override
  String get savedAccountDelete => 'Supprimer';

  @override
  String get savedAccountRipCopied => 'Compte RIP copié';

  @override
  String get savedAccountDeleteTitle => 'Supprimer';

  @override
  String savedAccountDeleteConfirm(String ownerName) {
    return 'Supprimer le compte de $ownerName ?';
  }

  @override
  String get savedAccountQrTitle => 'Scan code QR';

  @override
  String get savedAccountQrClose => 'Fermer';

  @override
  String get savedAccountSaveButton => 'Enregistrer le compte';

  @override
  String get verifyRipCopyButtonText => 'Copier';

  @override
  String get verifyRipSaveButtonText => 'Sauver';

  @override
  String get ccpFormInputInstruction =>
      'Saisissez un code CCP sans clé (10 chiffres maximum)';

  @override
  String get appInfoButtonText => 'Plus d\'informations';

  @override
  String get appInfoScreenTitle => 'À propos de CCP RIP DZ';

  @override
  String get appInfoContent =>
      '📄 À propos de CCP RIP DZ\nBienvenue sur notre application CCP RIP DZ !\nNotre application vous offre une solution simple et efficace pour gérer vos comptes CCP en Algérie. Voici ce que vous pouvez faire avec notre outil :\n\n🔧 Fonctionnalités principales :\n\n✅ Calcul du RIP : \nCalculez facilement le RIP (Relevé d\'Identité Postale) à partir de votre numéro CCP sans clé.\n\n💾 Sauvegarde des comptes : \nEnregistrez les comptes calculés pour un accès rapide.\n\n📤 Partage simplifié :\n    - Copier le code RIP \n    - Générer un Code QR \n    - Partager le compte via vos applications préférées \n\n🔍 Vérification du RIP : \nVérifiez la validité d\'un RIP existant :\n\n     - En saisissant manuellement le code RIP \n     - Ou en scannant un QR Code';

  @override
  String get ccpCalculationSuccess => 'Calcul effectué avec succès';

  @override
  String get ccpCalculationReady => 'Votre code RIP est prêt à être utilisé';

  @override
  String get ccpKeyCcp => 'Clé CCP';

  @override
  String get ccpKeyRip => 'Clé RIP';

  @override
  String get ccpAccountRip => 'Compte RIP';

  @override
  String get ccpCopyButton => 'Copier';

  @override
  String get ccpCopiedMessage => 'Copié';

  @override
  String get ccpSaveButton => 'ENREGISTRER LE COMPTE';
}
