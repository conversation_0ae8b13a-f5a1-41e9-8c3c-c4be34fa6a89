{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\AMEUR\\dzccp\\dzccp_rip\\android\\app\\.cxx\\Debug\\3g5f3c5d\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\AMEUR\\dzccp\\dzccp_rip\\android\\app\\.cxx\\Debug\\3g5f3c5d\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}