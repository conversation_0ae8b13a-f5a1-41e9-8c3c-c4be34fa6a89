import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/locale_provider.dart';
import '../l10n/app_localizations.dart';
import '../models/ccp_account.dart';
import '../screens/app_info_screen.dart';
import '../widgets/app_drawer.dart';
import '../widgets/banner_ad_widget.dart';
import '../widgets/ccp_form.dart';
import 'main_screen.dart'; // Import MainScreen to access static menu builder

// Import showcaseview if not already (though Showcase itself will be in CCPForm)
// import 'package:showcaseview/showcaseview.dart';

/// Écran d'accueil de l'application
class HomeScreen extends StatefulWidget {
  final GlobalKey? keyCcpInput;
  final GlobalKey? keySaveButton;
  final GlobalKey? keyResultsSection;

  const HomeScreen({
    super.key,
    this.keyCcpInput,
    this.keySaveButton,
    this.keyResultsSection,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

// Enum MenuChoice and _handleMenuChoice method removed from here

class _HomeScreenState extends State<HomeScreen> {
  String _getLanguageDisplay(Locale? locale) {
    final languageCode = locale?.languageCode ?? 'en';
    switch (languageCode) {
      case 'ar':
        return 'ع';
      case 'fr':
        return 'Fr';
      default:
        return 'En';
    }
  }
  CCPAccount? _calculatedAccount;
  bool _isLogoVisible = true; // Controls logo visibility, true by default

  // _handleMenuChoice method removed from here

  @override
  Widget build(BuildContext context) {
    // final bool isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    final loc = AppLocalizations.of(context);

    return Scaffold(
      drawer: const AppDrawer(),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: Text(
          loc.homeScreenTitle, // Use localized title
          overflow: TextOverflow.visible, // Permet au texte de déborder si nécessaire
          style: const TextStyle(fontSize: 16), // Taille de police réduite
        ),
        titleSpacing: 0, // Réduit l'espace entre le titre et les actions
        actions: [
          IconButton(
            icon: Consumer<LocaleProvider>(
              builder: (context, localeProvider, _) => Container(
                width: 18, // Taille réduite
                height: 18, // Taille réduite
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.fromBorderSide(
                    BorderSide(color: Colors.white, width: 1.0)
                  ),
                ),
                child: Center(
                  child: Text(
                    _getLanguageDisplay(localeProvider.locale),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 9, // Taille réduite
                      height: 1.0
                    ),
                  ),
                ),
              ),
            ),
            tooltip: loc.changeLanguageTooltip, // Use localized tooltip
            onPressed: () {
              showLanguageSelectionDialog(context);
            },
            padding: const EdgeInsets.symmetric(horizontal: 4), // Réduit le padding horizontal
            constraints: const BoxConstraints(), // Supprime les contraintes de taille minimale
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
            // Logo: Animates out upwards and stays hidden
            AnimatedContainer(
              duration: const Duration(milliseconds: 300), // Duration for height change
              curve: Curves.easeOut, // Curve for the height animation
              height: _isLogoVisible ? (100.0 + 2 * 24.0 + 40.0) : 0.0, // Target height (logo + padding + welcome text) or 0
              // clipBehavior: Clip.hardEdge, // Removed to avoid assertion error
              child: ClipRect(
                child: AnimatedOpacity(
                  opacity: _isLogoVisible ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 250),
                  child: OverflowBox(
                    alignment: Alignment.topCenter,
                    maxHeight: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 24.0),
                      child: Column(
                        children: [
                          Image.asset(
                            'assets/images/logo1.png',
                            height: 100,
                          ),
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.green.shade50,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: Colors.green.shade300),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.check_circle, color: Colors.green.shade700, size: 16),
                                const SizedBox(width: 8),
                                Text(
                                  'No Login Required',
                                  style: TextStyle(
                                    color: Colors.green.shade700,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ), // Correctly closes AnimatedOpacity
              ), // Correctly closes ClipRect
            ), // Correctly closes AnimatedContainer

            // En-tête
            // Cet espace sera toujours présent. Si l'image est masquée,
            // le formulaire commencera un peu plus haut.
            const SizedBox(height: 16),

            // Formulaire CCP
            CCPForm(
              keyCcpInput: widget.keyCcpInput,
              keySaveButton: widget.keySaveButton,
              keyResultsSection: widget.keyResultsSection,
              onCalculate: (account) {
                setState(() {
                  _calculatedAccount = account;
                });
              },
              onFocusChange: (hasFocus) {
                if (hasFocus && _isLogoVisible) {
                  setState(() {
                    _isLogoVisible = false;
                  });
                }
              },
            ),

            // Petit espace avant le bouton d'information
            const SizedBox(height: 16),

            // Bouton "Plus d'informations" en bas de la page (version minimisée)
            if (_calculatedAccount == null) // Afficher seulement si aucun résultat n'est affiché
              Align(
                // Aligner à l'opposé du texte d'instruction (à gauche pour l'arabe, à droite pour le français/anglais)
                alignment: Localizations.localeOf(context).languageCode == 'ar'
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4), // Marges réduites
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(15),
                    borderRadius: BorderRadius.circular(12), // Rayon réduit
                    border: Border.all(
                      color: Colors.blue.withAlpha(30),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12), // Rayon réduit
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AppInfoScreen(),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12), // Padding réduit
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.info_outline,
                              color: Colors.blue,
                              size: 16, // Taille réduite
                            ),
                            const SizedBox(width: 8),
                            Text(
                              loc.appInfoButtonText,
                              style: const TextStyle(
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                                fontSize: 12, // Taille réduite
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
                ],
              ),
            ),
          ),

          // Bannière publicitaire en bas
          const AdaptiveBannerAdWidget(),
        ],
      ),
    );
  }
}
