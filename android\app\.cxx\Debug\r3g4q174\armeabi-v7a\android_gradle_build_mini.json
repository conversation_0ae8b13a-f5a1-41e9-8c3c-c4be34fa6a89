{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\AMEUR\\dzccp\\dzccp_rip\\android\\app\\.cxx\\Debug\\r3g4q174\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\AMEUR\\dzccp\\dzccp_rip\\android\\app\\.cxx\\Debug\\r3g4q174\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}