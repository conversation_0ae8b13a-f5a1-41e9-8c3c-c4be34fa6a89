{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/AMEUR_5/CCP_RIP/android/app/.cxx/Debug/6x2s4r15/armeabi-v7a", "source": "C:/Users/<USER>/dev/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}